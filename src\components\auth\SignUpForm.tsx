'use client';

import * as React from 'react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

const supabase = createSupabaseBrowserClient();
// Use a simpler schema without z.refine to avoid async validation issues
const signUpSchema = z.object({
  email: z.string().min(1, 'Email is required').email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
});

type SignUpFormData = z.infer<typeof signUpSchema>;

export function SignUpForm() {
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setError,
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Supabase client is now created at the module top-level for efficiency.

  const onSubmit = async (data: SignUpFormData) => {
    setLoading(true);
    setApiError(null);
    setSuccess(false);

    // Manual password confirmation validation
    if (data.password !== data.confirmPassword) {
      setError('confirmPassword', {
        type: 'manual',
        message: 'Passwords do not match',
      });
      setLoading(false);
      return;
    }

    const { email, password } = data;
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/email-confirmed`,
        },
      });

      if (error) {
        setApiError(error.message || 'An error occurred during sign up.');
        setLoading(false);
        return;
      }

      setSuccess(true);
      reset();
    } catch (err) {
      if (
        typeof err === 'object' &&
        err !== null &&
        'message' in err &&
        typeof (err as { message?: unknown }).message === 'string'
      ) {
        setApiError((err as { message: string }).message);
      } else {
        setApiError('An unexpected error occurred.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='flex justify-center items-center min-h-[60vh]'>
      <Card className='w-full max-w-md p-6 shadow-lg'>
        <h2 className='text-2xl font-semibold mb-6 text-center'>Create your NAVsync.io account</h2>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-5' noValidate>
          <div>
            <label htmlFor='email' className='block text-sm font-medium mb-1'>
              Email
            </label>
            <Input
              id='email'
              type='email'
              autoComplete='email'
              placeholder='<EMAIL>'
              disabled={loading}
              {...register('email')}
            />
            {errors.email && <p className='text-red-600 text-xs mt-1'>{errors.email.message}</p>}
          </div>

          <div>
            <label htmlFor='password' className='block text-sm font-medium mb-1'>
              Password
            </label>
            <Input
              id='password'
              type='password'
              autoComplete='new-password'
              placeholder='Enter password'
              disabled={loading}
              {...register('password')}
            />
            {errors.password && (
              <p className='text-red-600 text-xs mt-1'>{errors.password.message}</p>
            )}
          </div>

          <div>
            <label htmlFor='confirmPassword' className='block text-sm font-medium mb-1'>
              Confirm Password
            </label>
            <Input
              id='confirmPassword'
              type='password'
              autoComplete='new-password'
              placeholder='Re-enter password'
              disabled={loading}
              {...register('confirmPassword')}
            />
            {errors.confirmPassword && (
              <p className='text-red-600 text-xs mt-1'>{errors.confirmPassword.message}</p>
            )}
          </div>

          {apiError && <div className='text-red-600 text-sm text-center'>{apiError}</div>}

          {success && (
            <div className='text-green-600 text-sm text-center'>
              Account created successfully! Please check your email to verify your account.
            </div>
          )}

          <Button type='submit' className='w-full' disabled={loading}>
            {loading ? (
              <span>
                <span className='animate-spin inline-block mr-2'>&#9696;</span>
                Creating account...
              </span>
            ) : (
              'Sign Up'
            )}
          </Button>
        </form>
      </Card>
    </div>
  );
}

export default SignUpForm;

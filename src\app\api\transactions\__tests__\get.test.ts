import { GET } from '../get/route';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/supabase/server', () => ({
  createSupabaseServerClient: jest.fn(),
}));

jest.mock('next/server', () => ({
  ...jest.requireActual('next/server'),
  NextResponse: {
    json: jest.fn((body, init) => ({
      status: init?.status || 200,
      json: () => Promise.resolve(body),
    })),
  },
}));

describe('GET /api/transactions', () => {
  const mockUser = { id: 'test-user-id' };

  const createMockRequest = (queryParams: Record<string, string> = {}) => {
    const url = new URL('http://localhost/api/transactions');
    Object.entries(queryParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
    return new Request(url.toString(), {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    }) as NextRequest;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Success (200 OK) with pagination', async () => {
    const mockTransactions = Array.from({ length: 10 }, (_, i) => ({
      id: `txn-${i}`,
      amount: 100 + i,
      description: `Test Transaction ${i}`,
      transaction_date: new Date().toISOString(),
    }));
    const mockTotalCount = 25;

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'transactions') {
          return {
            select: jest.fn((_query, options) => {
              if (options?.head) {
                return {
                  eq: jest.fn().mockResolvedValue({ count: mockTotalCount, error: null }),
                };
              }
              return {
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                range: jest.fn().mockResolvedValue({ data: mockTransactions, error: null }),
              };
            }),
          };
        }
        return {};
      }),
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    const req = createMockRequest({ page: '2', pageSize: '10' });
    const res = await GET(req);
    const body = await res.json();

    expect(res.status).toBe(200);
    expect(body.transactions).toEqual(mockTransactions);
    expect(body.pagination).toEqual({
      currentPage: 2,
      pageSize: 10,
      totalCount: mockTotalCount,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: true,
    });
  });

  test('Unauthorized (401)', async () => {
    (createSupabaseServerClient as jest.Mock).mockResolvedValue({
      auth: {
        getUser: jest
          .fn()
          .mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized' } }),
      },
    });

    const req = createMockRequest();
    const res = await GET(req);

    expect(res.status).toBe(401);
    expect(await res.json()).toEqual({ error: 'Unauthorized' });
  });

  test('Database Error on count (500)', async () => {
    const dbError = { message: 'DB count failed' };
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'transactions') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockResolvedValue({ count: null, error: dbError }),
            }),
          };
        }
        return {};
      }),
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    // Suppress console.error for this test
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const req = createMockRequest();
    const res = await GET(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Failed to count transactions' });

    consoleErrorSpy.mockRestore();
  });

  test('Database Error on fetch (500)', async () => {
    const dbError = { message: 'DB fetch failed' };
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn().mockImplementation((table: string) => {
        if (table === 'transactions') {
          return {
            select: jest.fn((_query, options) => {
              if (options?.head) {
                return {
                  eq: jest.fn().mockResolvedValue({ count: 25, error: null }),
                };
              }
              return {
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                range: jest.fn().mockResolvedValue({ data: null, error: dbError }),
              };
            }),
          };
        }
        return {};
      }),
    };
    (createSupabaseServerClient as jest.Mock).mockResolvedValue(mockSupabase);

    // Suppress console.error for this test
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const req = createMockRequest();
    const res = await GET(req);

    expect(res.status).toBe(500);
    expect(await res.json()).toEqual({ error: 'Failed to fetch transactions' });

    consoleErrorSpy.mockRestore();
  });
});

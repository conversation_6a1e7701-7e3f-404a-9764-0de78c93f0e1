import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SignUpForm from '../SignUpForm';

// Mock window.location

// Mock the Supabase client module
jest.mock('@/lib/supabase/client', () => {
  const mockAuth = {
    signUp: jest.fn(),
  };

  return {
    createSupabaseBrowserClient: jest.fn(() => ({
      auth: mockAuth,
    })),
    // Export the mock for test access
    __mockAuth: mockAuth,
  };
});

// Get access to the mock auth object
const { __mockAuth: mockAuth } = jest.requireMock('@/lib/supabase/client');

describe('SignUpForm Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('Successful Registration', () => {
    it('should show success message on successful registration', async () => {
      const user = userEvent.setup();

      // Mock successful registration
      mockAuth.signUp.mockResolvedValue({
        error: null,
        data: { user: { id: '123', email: '<EMAIL>' } },
      });

      render(<SignUpForm />);

      // Fill out the form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');

      // Submit the form
      await user.click(submitButton);

      // Wait for the async operation to complete
      await waitFor(() => {
        expect(mockAuth.signUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
          options: {
            emailRedirectTo: 'http://localhost:3000/auth/email-confirmed',
          },
        });
      });

      // Assert that success message is shown
      expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
      expect(screen.getByText(/please check your email to verify/i)).toBeInTheDocument();
    });

    it('should show loading state during registration', async () => {
      const user = userEvent.setup();

      // Mock a delayed response
      mockAuth.signUp.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve({ error: null }), 100))
      );

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      // Check that loading state is shown
      expect(screen.getByText(/creating account/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
      expect(confirmPasswordInput).toBeDisabled();

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText(/creating account/i)).not.toBeInTheDocument();
      });
    });

    it('should reset form fields after successful registration', async () => {
      const user = userEvent.setup();

      // Mock successful registration
      mockAuth.signUp.mockResolvedValue({
        error: null,
        data: { user: { id: '123', email: '<EMAIL>' } },
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      // Wait for success and form reset
      await waitFor(() => {
        expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
      });

      // Check that form fields are cleared
      expect(emailInput).toHaveValue('');
      expect(passwordInput).toHaveValue('');
      expect(confirmPasswordInput).toHaveValue('');
    });
  });

  describe('Failed Registration', () => {
    it('should display error message on registration failure', async () => {
      const user = userEvent.setup();

      // Mock registration error
      mockAuth.signUp.mockResolvedValue({
        error: { message: 'User already registered' },
        data: null,
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      // Wait for error message to appear
      await waitFor(() => {
        expect(screen.getByText('User already registered')).toBeInTheDocument();
      });

      // Ensure success message is not shown
      expect(screen.queryByText(/account created successfully/i)).not.toBeInTheDocument();
    });

    it('should display fallback error message when error has no message', async () => {
      const user = userEvent.setup();

      // Mock registration error without message
      mockAuth.signUp.mockResolvedValue({
        error: {},
        data: null,
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('An error occurred during sign up.')).toBeInTheDocument();
      });
    });

    it('should handle unexpected errors during registration', async () => {
      const user = userEvent.setup();

      // Mock unexpected error
      mockAuth.signUp.mockRejectedValue(new Error('Network error'));

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });
    });

    it('should handle non-Error exceptions', async () => {
      const user = userEvent.setup();

      // Mock non-Error exception
      mockAuth.signUp.mockRejectedValue('String error');

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('An unexpected error occurred.')).toBeInTheDocument();
      });
    });

    it('should clear previous error messages on new submission', async () => {
      const user = userEvent.setup();

      // First submission with error
      mockAuth.signUp.mockResolvedValueOnce({
        error: { message: 'User already registered' },
        data: null,
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('User already registered')).toBeInTheDocument();
      });

      // Second submission with success
      mockAuth.signUp.mockResolvedValueOnce({
        error: null,
        data: { user: { id: '123' } },
      });

      await user.clear(emailInput);
      await user.clear(passwordInput);
      await user.clear(confirmPasswordInput);
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'newpassword123');
      await user.type(confirmPasswordInput, 'newpassword123');
      await user.click(submitButton);

      // Error message should be cleared
      await waitFor(() => {
        expect(screen.queryByText('User already registered')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    // Note: Email validation test skipped due to react-hook-form validation timing
    // This indicates an inconsistency between LoginForm and SignUpForm validation approaches
    it('should show validation error for invalid email format', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      // Enter invalid email but valid passwords to isolate the email validation issue
      await user.type(emailInput, 'invalid-email');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');

      await user.click(submitButton);

      // Wait for validation to complete and check that API was not called
      await waitFor(
        () => {
          expect(mockAuth.signUp).not.toHaveBeenCalled();
        },
        { timeout: 1000 }
      );

      // Check for the validation error message
      await waitFor(
        () => {
          expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
        },
        { timeout: 2000 }
      );
    });

    it('should show validation error for short password', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, '123'); // Too short
      await user.type(confirmPasswordInput, '123');
      await user.click(submitButton);

      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
      expect(mockAuth.signUp).not.toHaveBeenCalled();
    });

    it('should show validation error for password mismatch', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'differentpassword');
      await user.click(submitButton);

      expect(screen.getByText('Passwords do not match')).toBeInTheDocument();
      expect(mockAuth.signUp).not.toHaveBeenCalled();
    });

    it('should show validation errors for empty fields', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const submitButton = screen.getByRole('button', { name: /sign up/i });

      // Submit empty form
      await user.click(submitButton);

      // All validation errors should appear
      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
      expect(screen.getByText('Please confirm your password')).toBeInTheDocument();
      expect(mockAuth.signUp).not.toHaveBeenCalled();
    });

    it('should show validation error for empty confirm password when password is filled', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      // Don't fill confirm password
      await user.click(submitButton);

      expect(screen.getByText('Please confirm your password')).toBeInTheDocument();
      expect(mockAuth.signUp).not.toHaveBeenCalled();
    });

    it('should remove validation errors when fields become valid', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      // First, trigger validation errors
      await user.click(submitButton);

      expect(screen.getByText('Email is required')).toBeInTheDocument();
      expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
      expect(screen.getByText('Please confirm your password')).toBeInTheDocument();

      // Then fix the fields
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');

      // Validation errors should be cleared
      await waitFor(() => {
        expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
        expect(
          screen.queryByText('Password must be at least 6 characters')
        ).not.toBeInTheDocument();
        expect(screen.queryByText('Please confirm your password')).not.toBeInTheDocument();
      });
    });

    it('should clear password mismatch error when passwords match', async () => {
      const user = userEvent.setup();

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      // First, create password mismatch
      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'differentpassword');
      await user.click(submitButton);

      expect(screen.getByText('Passwords do not match')).toBeInTheDocument();

      // Fix the confirm password
      await user.clear(confirmPasswordInput);
      await user.type(confirmPasswordInput, 'password123');

      // Password mismatch error should be cleared
      await waitFor(() => {
        expect(screen.queryByText('Passwords do not match')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Accessibility', () => {
    it('should have proper form structure and labels', () => {
      render(<SignUpForm />);

      // Check for proper form structure (form element exists)
      const formElement = screen.getByRole('button', { name: /sign up/i }).closest('form');
      expect(formElement).toBeInTheDocument();

      // Check for proper labels
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();

      // Check for submit button
      expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
    });

    it('should have proper input types and autocomplete attributes', () => {
      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      // Check input types
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(confirmPasswordInput).toHaveAttribute('type', 'password');

      // Check autocomplete attributes
      expect(emailInput).toHaveAttribute('autocomplete', 'email');
      expect(passwordInput).toHaveAttribute('autocomplete', 'new-password');
      expect(confirmPasswordInput).toHaveAttribute('autocomplete', 'new-password');
    });

    it('should have proper placeholder text', () => {
      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(passwordInput).toHaveAttribute('placeholder', 'Enter password');
      expect(confirmPasswordInput).toHaveAttribute('placeholder', 'Re-enter password');
    });

    it('should have proper heading structure', () => {
      render(<SignUpForm />);

      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent('Create your NAVsync.io account');
    });

    it('should disable all form elements during loading', async () => {
      const user = userEvent.setup();

      mockAuth.signUp.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve({ error: null }), 100))
      );

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      expect(emailInput).toBeDisabled();
      expect(passwordInput).toBeDisabled();
      expect(confirmPasswordInput).toBeDisabled();
      expect(submitButton).toBeDisabled();

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });
    });

    it('should have proper success message styling and content', async () => {
      const user = userEvent.setup();

      mockAuth.signUp.mockResolvedValue({
        error: null,
        data: { user: { id: '123' } },
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        const successMessage = screen.getByText(
          /Account created successfully! Please check your email to verify your account./i
        );
        expect(successMessage).toBeInTheDocument();
        expect(successMessage).toHaveClass('text-green-600');
      });
    });

    it('should have proper error message styling', async () => {
      const user = userEvent.setup();

      mockAuth.signUp.mockResolvedValue({
        error: { message: 'Test error' },
        data: null,
      });

      render(<SignUpForm />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        const errorMessage = screen.getByText('Test error');
        expect(errorMessage).toBeInTheDocument();
        expect(errorMessage).toHaveClass('text-red-600');
      });
    });
  });
});
